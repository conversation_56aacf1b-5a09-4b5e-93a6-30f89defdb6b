#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import traceback

try:
    print("正在导入模块...")
    from Hierarchical_MOA_ThetaStar12345 import HierarchicalMOAThetaStar
    print("模块导入成功")
    
    print("正在创建类实例...")
    planner = HierarchicalMOAThetaStar()
    print("类实例创建成功")
    
    print("检查方法是否存在...")
    has_method = hasattr(planner, 'generate_diverse_weights')
    print(f"generate_diverse_weights 方法存在: {has_method}")
    
    if has_method:
        print("尝试调用方法...")
        result = planner.generate_diverse_weights(5)
        print(f"方法调用成功，返回结果: {result}")
    else:
        print("可用方法列表:")
        methods = [method for method in dir(planner) if not method.startswith('_')]
        for method in methods:
            print(f"  - {method}")
            
except Exception as e:
    print(f"错误: {e}")
    print("详细错误信息:")
    traceback.print_exc()
